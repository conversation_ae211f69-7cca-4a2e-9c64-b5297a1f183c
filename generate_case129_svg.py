#!/usr/bin/env python3
"""
Generate SVG for case129.m MATPOWER case file.
"""

from matpower_to_svg import MatpowerToSVGConverter, ConversionConfig
from svg_generator import SVGStyle
from layout_algorithms import LayoutConfig

def main():
    # Create custom configuration for case129
    config = ConversionConfig()
    
    # Layout settings - larger canvas for 129-bus system
    config.layout_config = LayoutConfig(
        width=1400.0,
        height=1000.0,
        margin=60.0,
        bus_spacing=35.0,
        level_spacing=70.0,
        spring_iterations=150,
        spring_k=0.8,
        spring_repulsion=1200.0
    )
    
    # SVG styling - optimized for larger system
    config.svg_style = SVGStyle(
        width=1400.0,
        height=1000.0,
        background_color="white",
        
        # Bus styling
        bus_radius=3.5,
        bus_color="black",
        bus_stroke_width=1.0,
        
        # Bus type colors
        pq_bus_color="darkblue",
        pv_bus_color="green", 
        ref_bus_color="red",
        isolated_bus_color="gray",
        
        # Line styling
        line_color="darkgray",
        line_width=1.0,
        line_opacity=0.8,
        
        # Label styling
        label_font_family="Arial, sans-serif",
        label_font_size=9.0,
        label_color="black",
        
        # Generator symbols
        gen_symbol_size=6.0,
        gen_symbol_color="forestgreen",
        
        # Title and caption
        title_font_size=18.0,
        title_color="darkblue",
        caption_font_size=14.0,
        caption_color="darkgreen"
    )
    
    # Set algorithm and title
    config.layout_algorithm = "hierarchical"  # Try different algorithms: "spring", "radial"
    config.title = "Case129 - 129-Bus Distribution System Topology"
    config.output_file = "case129_topology.svg"
    
    # Create converter and generate SVG
    converter = MatpowerToSVGConverter(config)
    
    try:
        print("Parsing case129.m...")
        svg_content = converter.convert_file("case129.m")
        print(f"SVG generated successfully: {config.output_file}")
        
        # Also try spring layout for comparison
        print("\nGenerating spring layout version...")
        config.layout_algorithm = "spring"
        config.output_file = "case129_topology_spring.svg"
        config.title = "Case129 - 129-Bus Distribution System (Spring Layout)"
        
        converter_spring = MatpowerToSVGConverter(config)
        svg_content_spring = converter_spring.convert_file("case129.m")
        print(f"Spring layout SVG generated: {config.output_file}")
        
        # Try radial layout too
        print("\nGenerating radial layout version...")
        config.layout_algorithm = "radial"
        config.output_file = "case129_topology_radial.svg"
        config.title = "Case129 - 129-Bus Distribution System (Radial Layout)"
        
        converter_radial = MatpowerToSVGConverter(config)
        svg_content_radial = converter_radial.convert_file("case129.m")
        print(f"Radial layout SVG generated: {config.output_file}")
        
        print("\nAll SVG files generated successfully!")
        print("Files created:")
        print("- case129_topology.svg (hierarchical layout)")
        print("- case129_topology_spring.svg (spring layout)")
        print("- case129_topology_radial.svg (radial layout)")
        
    except Exception as e:
        print(f"Error generating SVG: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
