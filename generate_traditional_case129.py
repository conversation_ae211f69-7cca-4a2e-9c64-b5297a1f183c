#!/usr/bin/env python3
"""
Generate traditional distribution system style SVG for case129.m
"""

from matpower_parser import MatpowerParser
from traditional_svg_generator import TraditionalSVGGenerator

def main():
    # Parse the MATPOWER case
    parser = MatpowerParser()
    case = parser.parse_file("case129.m")
    
    print(f"Parsed {case.name}: {case.get_bus_count()} buses, {case.get_branch_count()} branches")
    
    # Generate traditional style SVG
    generator = TraditionalSVGGenerator()
    svg_content = generator.generate_svg(case)
    
    # Save to file
    output_file = "case129_traditional.svg"
    with open(output_file, 'w') as f:
        f.write(svg_content)
    
    print(f"Traditional style SVG saved to: {output_file}")

if __name__ == "__main__":
    main()
