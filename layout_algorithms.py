"""
Layout Algorithms for Power System Topology Visualization

This module provides various algorithms for automatically positioning buses
and routing transmission lines in power system network diagrams.
"""

import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from matpower_parser import MatpowerCase, F_BUS, T_BUS, BUS_I, BUS_TYPE


@dataclass
class BusPosition:
    """Represents the position of a bus in the layout."""
    x: float
    y: float
    bus_id: int
    bus_type: int = 1  # 1=PQ, 2=PV, 3=ref, 4=isolated


@dataclass
class LayoutConfig:
    """Configuration parameters for layout algorithms."""
    width: float = 1000.0
    height: float = 800.0
    margin: float = 50.0
    bus_spacing: float = 40.0
    level_spacing: float = 80.0
    spring_iterations: int = 100
    spring_k: float = 1.0
    spring_repulsion: float = 1000.0


class NetworkLayoutEngine:
    """Engine for generating network layouts from MATPOWER cases."""
    
    def __init__(self, config: Optional[LayoutConfig] = None):
        self.config = config or LayoutConfig()
        self.positions = {}
        self.graph = None
        
    def generate_layout(self, case: MatpowerCase, algorithm: str = "hierarchical") -> Dict[int, BusPosition]:
        """
        Generate layout positions for all buses in the power system.

        Args:
            case: MATPOWER case data
            algorithm: Layout algorithm to use ('hierarchical', 'spring', 'radial', 'geographic', 'distribution')

        Returns:
            Dictionary mapping bus IDs to BusPosition objects
        """
        # Build network graph
        self.graph = self._build_network_graph(case)

        # Apply selected algorithm
        if algorithm == "hierarchical":
            return self._hierarchical_layout(case)
        elif algorithm == "spring":
            return self._spring_layout(case)
        elif algorithm == "radial":
            return self._radial_layout(case)
        elif algorithm == "geographic":
            return self._geographic_layout(case)
        elif algorithm == "distribution":
            return self._distribution_layout(case)
        else:
            raise ValueError(f"Unknown layout algorithm: {algorithm}")
    
    def _build_network_graph(self, case: MatpowerCase) -> nx.Graph:
        """Build a NetworkX graph from the MATPOWER case."""
        G = nx.Graph()
        
        # Add buses as nodes
        if len(case.bus.shape) > 1:
            for bus_data in case.bus:
                bus_id = int(bus_data[BUS_I])
                bus_type = int(bus_data[BUS_TYPE])
                G.add_node(bus_id, bus_type=bus_type)
        
        # Add branches as edges
        if len(case.branch.shape) > 1:
            for branch_data in case.branch:
                from_bus = int(branch_data[F_BUS])
                to_bus = int(branch_data[T_BUS])
                G.add_edge(from_bus, to_bus)
        
        return G
    
    def _hierarchical_layout(self, case: MatpowerCase) -> Dict[int, BusPosition]:
        """
        Generate hierarchical layout based on network topology.
        Places reference bus at top and arranges others by electrical distance.
        """
        positions = {}
        
        if not self.graph or self.graph.number_of_nodes() == 0:
            return positions
        
        # Find reference bus (type 3) or use first bus
        ref_bus = None
        for bus_data in case.bus:
            if int(bus_data[BUS_TYPE]) == 3:  # Reference bus
                ref_bus = int(bus_data[BUS_I])
                break
        
        if ref_bus is None:
            ref_bus = int(case.bus[0][BUS_I])
        
        # Calculate levels using BFS from reference bus
        levels = self._calculate_bus_levels(ref_bus)
        
        # Arrange buses by levels
        level_buses = {}
        for bus_id, level in levels.items():
            if level not in level_buses:
                level_buses[level] = []
            level_buses[level].append(bus_id)
        
        # Position buses
        max_level = max(level_buses.keys()) if level_buses else 0
        
        for level, buses in level_buses.items():
            y = self.config.margin + level * self.config.level_spacing
            
            # Center buses horizontally at each level
            total_width = len(buses) * self.config.bus_spacing
            start_x = (self.config.width - total_width) / 2
            
            for i, bus_id in enumerate(sorted(buses)):
                x = start_x + i * self.config.bus_spacing
                
                # Get bus type
                bus_type = 1
                for bus_data in case.bus:
                    if int(bus_data[BUS_I]) == bus_id:
                        bus_type = int(bus_data[BUS_TYPE])
                        break
                
                positions[bus_id] = BusPosition(x, y, bus_id, bus_type)
        
        return positions
    
    def _spring_layout(self, case: MatpowerCase) -> Dict[int, BusPosition]:
        """Generate layout using spring-force algorithm."""
        if not self.graph or self.graph.number_of_nodes() == 0:
            return {}
        
        # Use NetworkX spring layout
        pos = nx.spring_layout(
            self.graph,
            k=self.config.spring_k,
            iterations=self.config.spring_iterations,
            scale=min(self.config.width, self.config.height) / 2
        )
        
        positions = {}
        for bus_id, (x, y) in pos.items():
            # Scale and center the positions
            scaled_x = x + self.config.width / 2
            scaled_y = y + self.config.height / 2
            
            # Get bus type
            bus_type = 1
            for bus_data in case.bus:
                if int(bus_data[BUS_I]) == bus_id:
                    bus_type = int(bus_data[BUS_TYPE])
                    break
            
            positions[bus_id] = BusPosition(scaled_x, scaled_y, bus_id, bus_type)
        
        return positions
    
    def _radial_layout(self, case: MatpowerCase) -> Dict[int, BusPosition]:
        """Generate radial layout with reference bus at center."""
        positions = {}
        
        if not self.graph or self.graph.number_of_nodes() == 0:
            return positions
        
        # Find reference bus
        ref_bus = None
        for bus_data in case.bus:
            if int(bus_data[BUS_TYPE]) == 3:
                ref_bus = int(bus_data[BUS_I])
                break
        
        if ref_bus is None:
            ref_bus = int(case.bus[0][BUS_I])
        
        # Place reference bus at center
        center_x = self.config.width / 2
        center_y = self.config.height / 2
        
        bus_type = 3
        for bus_data in case.bus:
            if int(bus_data[BUS_I]) == ref_bus:
                bus_type = int(bus_data[BUS_TYPE])
                break
        
        positions[ref_bus] = BusPosition(center_x, center_y, ref_bus, bus_type)
        
        # Calculate levels and arrange in concentric circles
        levels = self._calculate_bus_levels(ref_bus)
        
        level_buses = {}
        for bus_id, level in levels.items():
            if level > 0:  # Skip reference bus (level 0)
                if level not in level_buses:
                    level_buses[level] = []
                level_buses[level].append(bus_id)
        
        # Arrange buses in concentric circles
        for level, buses in level_buses.items():
            radius = level * 80  # Radius increases with level
            angle_step = 2 * np.pi / len(buses)
            
            for i, bus_id in enumerate(buses):
                angle = i * angle_step
                x = center_x + radius * np.cos(angle)
                y = center_y + radius * np.sin(angle)
                
                # Get bus type
                bus_type = 1
                for bus_data in case.bus:
                    if int(bus_data[BUS_I]) == bus_id:
                        bus_type = int(bus_data[BUS_TYPE])
                        break
                
                positions[bus_id] = BusPosition(x, y, bus_id, bus_type)
        
        return positions
    
    def _geographic_layout(self, case: MatpowerCase) -> Dict[int, BusPosition]:
        """
        Generate layout based on geographic coordinates if available.
        Falls back to hierarchical layout if no geographic data.
        """
        # For now, fall back to hierarchical layout
        # In future versions, this could read geographic coordinates from case data
        return self._hierarchical_layout(case)
    
    def _calculate_bus_levels(self, ref_bus: int) -> Dict[int, int]:
        """Calculate the level (electrical distance) of each bus from reference."""
        if not self.graph or ref_bus not in self.graph:
            return {}
        
        # Use BFS to find shortest path distances
        levels = nx.single_source_shortest_path_length(self.graph, ref_bus)
        return levels
    
    def optimize_layout(self, positions: Dict[int, BusPosition], case: MatpowerCase) -> Dict[int, BusPosition]:
        """
        Optimize layout to reduce line crossings and improve readability.
        
        Args:
            positions: Initial positions
            case: MATPOWER case data
            
        Returns:
            Optimized positions
        """
        # Simple optimization: spread out overlapping buses
        optimized = positions.copy()
        
        # Check for overlapping buses and adjust
        bus_ids = list(optimized.keys())
        for i, bus1_id in enumerate(bus_ids):
            for j, bus2_id in enumerate(bus_ids[i+1:], i+1):
                pos1 = optimized[bus1_id]
                pos2 = optimized[bus2_id]
                
                # Calculate distance
                dx = pos2.x - pos1.x
                dy = pos2.y - pos1.y
                distance = np.sqrt(dx*dx + dy*dy)
                
                # If too close, push apart
                min_distance = self.config.bus_spacing * 0.8
                if distance < min_distance and distance > 0:
                    # Calculate push direction
                    push_x = (dx / distance) * (min_distance - distance) / 2
                    push_y = (dy / distance) * (min_distance - distance) / 2
                    
                    # Update positions
                    optimized[bus1_id] = BusPosition(
                        pos1.x - push_x, pos1.y - push_y, pos1.bus_id, pos1.bus_type
                    )
                    optimized[bus2_id] = BusPosition(
                        pos2.x + push_x, pos2.y + push_y, pos2.bus_id, pos2.bus_type
                    )
        
        return optimized

    def _distribution_layout(self, case: MatpowerCase) -> Dict[int, BusPosition]:
        """
        Generate distribution system layout with main vertical bus and horizontal branches.
        Similar to traditional distribution system diagrams.
        """
        positions = {}

        if not self.graph or self.graph.number_of_nodes() == 0:
            return positions

        # Find reference bus or use first bus as main bus
        main_bus = None
        for bus_data in case.bus:
            if int(bus_data[BUS_TYPE]) == 3:  # Reference bus
                main_bus = int(bus_data[BUS_I])
                break

        if main_bus is None:
            main_bus = int(case.bus[0][BUS_I])

        # Main vertical bus position
        main_x = 100
        main_y_start = 80
        main_y_end = self.config.height - 120

        # Place main bus at top of vertical line
        positions[main_bus] = BusPosition(main_x, main_y_start, main_bus, 3)

        # Find all buses connected to main bus and organize into branches
        branches = self._organize_into_branches(main_bus)

        # Position branches along the main vertical line
        y_step = (main_y_end - main_y_start) / max(len(branches), 1)

        for i, branch_buses in enumerate(branches):
            branch_y = main_y_start + (i + 1) * y_step

            # Position buses horizontally in this branch
            branch_x_start = main_x + 50
            x_step = 40

            for j, bus_id in enumerate(branch_buses):
                if bus_id != main_bus:  # Skip main bus
                    x = branch_x_start + j * x_step

                    # Get bus type
                    bus_type = 1
                    for bus_data in case.bus:
                        if int(bus_data[BUS_I]) == bus_id:
                            bus_type = int(bus_data[BUS_TYPE])
                            break

                    positions[bus_id] = BusPosition(x, branch_y, bus_id, bus_type)

        return positions

    def _organize_into_branches(self, main_bus: int) -> List[List[int]]:
        """
        Organize network into branches emanating from the main bus.
        """
        if not self.graph:
            return []

        visited = {main_bus}
        branches = []

        # Find all neighbors of main bus
        main_neighbors = list(self.graph.neighbors(main_bus))

        for neighbor in main_neighbors:
            if neighbor not in visited:
                # Trace this branch using DFS
                branch = self._trace_branch(neighbor, visited.copy())
                if branch:
                    branches.append(branch)
                    visited.update(branch)

        # Handle any remaining unconnected buses
        remaining_buses = set(self.graph.nodes()) - visited
        if remaining_buses:
            # Group remaining buses into additional branches
            while remaining_buses:
                bus = remaining_buses.pop()
                branch = [bus]
                # Find connected components in remaining buses
                for other_bus in list(remaining_buses):
                    if self.graph.has_edge(bus, other_bus):
                        branch.append(other_bus)
                        remaining_buses.discard(other_bus)
                branches.append(branch)

        return branches

    def _trace_branch(self, start_bus: int, visited: set) -> List[int]:
        """
        Trace a branch from start_bus using DFS.
        """
        branch = []
        stack = [start_bus]

        while stack:
            current = stack.pop()
            if current not in visited:
                visited.add(current)
                branch.append(current)

                # Add unvisited neighbors to stack
                for neighbor in self.graph.neighbors(current):
                    if neighbor not in visited:
                        stack.append(neighbor)

        return branch
