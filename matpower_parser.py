"""
MATPOWER Case File Parser

This module provides functionality to parse MATPOWER case files (.m format)
and extract power system topology data including buses, branches, and generators.
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import os


class MatpowerCase:
    """Represents a parsed MATPOWER case with all relevant data."""
    
    def __init__(self):
        self.name = ""
        self.version = "2"
        self.baseMVA = 100.0
        self.bus = np.array([])
        self.gen = np.array([])
        self.branch = np.array([])
        self.gencost = np.array([])
        self.areas = np.array([])
        self.bus_names = {}
        
    def get_bus_count(self) -> int:
        """Return the number of buses in the system."""
        return len(self.bus) if len(self.bus.shape) > 1 else 0
    
    def get_branch_count(self) -> int:
        """Return the number of branches in the system."""
        return len(self.branch) if len(self.branch.shape) > 1 else 0
    
    def get_generator_count(self) -> int:
        """Return the number of generators in the system."""
        return len(self.gen) if len(self.gen.shape) > 1 else 0


class MatpowerParser:
    """Parser for MATPOWER case files."""
    
    def __init__(self):
        self.case = MatpowerCase()
        
    def parse_file(self, filepath: str) -> MatpowerCase:
        """
        Parse a MATPOWER case file and return a MatpowerCase object.
        
        Args:
            filepath: Path to the MATPOWER .m file
            
        Returns:
            MatpowerCase object containing parsed data
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"MATPOWER case file not found: {filepath}")
            
        with open(filepath, 'r') as f:
            content = f.read()
            
        self.case = MatpowerCase()
        self.case.name = os.path.splitext(os.path.basename(filepath))[0]
        
        # Parse different sections
        self._parse_base_mva(content)
        self._parse_bus_data(content)
        self._parse_generator_data(content)
        self._parse_branch_data(content)
        self._parse_generator_cost(content)
        self._parse_areas(content)
        
        return self.case
    
    def _parse_base_mva(self, content: str) -> None:
        """Parse the base MVA value."""
        pattern = r'mpc\.baseMVA\s*=\s*([0-9.]+);'
        match = re.search(pattern, content)
        if match:
            self.case.baseMVA = float(match.group(1))
    
    def _parse_bus_data(self, content: str) -> None:
        """Parse bus data matrix."""
        pattern = r'mpc\.bus\s*=\s*\[(.*?)\];'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            bus_data = self._parse_matrix(match.group(1))
            self.case.bus = np.array(bus_data)
    
    def _parse_generator_data(self, content: str) -> None:
        """Parse generator data matrix."""
        pattern = r'mpc\.gen\s*=\s*\[(.*?)\];'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            gen_data = self._parse_matrix(match.group(1))
            self.case.gen = np.array(gen_data)
    
    def _parse_branch_data(self, content: str) -> None:
        """Parse branch data matrix."""
        pattern = r'mpc\.branch\s*=\s*\[(.*?)\];'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            branch_data = self._parse_matrix(match.group(1))
            self.case.branch = np.array(branch_data)
    
    def _parse_generator_cost(self, content: str) -> None:
        """Parse generator cost data matrix."""
        pattern = r'mpc\.gencost\s*=\s*\[(.*?)\];'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            gencost_data = self._parse_matrix(match.group(1))
            self.case.gencost = np.array(gencost_data)
    
    def _parse_areas(self, content: str) -> None:
        """Parse areas data matrix if present."""
        pattern = r'mpc\.areas\s*=\s*\[(.*?)\];'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            areas_data = self._parse_matrix(match.group(1))
            self.case.areas = np.array(areas_data)
    
    def _parse_matrix(self, matrix_str: str) -> List[List[float]]:
        """
        Parse a MATLAB matrix string into a list of lists.
        
        Args:
            matrix_str: String representation of MATLAB matrix
            
        Returns:
            List of lists containing matrix data
        """
        # Remove comments and clean up
        lines = []
        for line in matrix_str.split('\n'):
            # Remove comments (everything after %)
            line = re.sub(r'%.*$', '', line)
            line = line.strip()
            if line:
                lines.append(line)
        
        matrix_data = []
        for line in lines:
            # Remove semicolons and split by whitespace
            line = line.rstrip(';').strip()
            if line:
                # Split by whitespace and convert to float
                row = []
                for value in line.split():
                    try:
                        row.append(float(value))
                    except ValueError:
                        # Skip non-numeric values
                        continue
                if row:
                    matrix_data.append(row)
        
        return matrix_data
    
    def parse_string(self, content: str, case_name: str = "unnamed") -> MatpowerCase:
        """
        Parse MATPOWER case data from a string.
        
        Args:
            content: String content of MATPOWER case
            case_name: Name for the case
            
        Returns:
            MatpowerCase object containing parsed data
        """
        self.case = MatpowerCase()
        self.case.name = case_name
        
        # Parse different sections
        self._parse_base_mva(content)
        self._parse_bus_data(content)
        self._parse_generator_data(content)
        self._parse_branch_data(content)
        self._parse_generator_cost(content)
        self._parse_areas(content)
        
        return self.case


def load_matpower_case(filepath: str) -> MatpowerCase:
    """
    Convenience function to load a MATPOWER case file.
    
    Args:
        filepath: Path to the MATPOWER .m file
        
    Returns:
        MatpowerCase object containing parsed data
    """
    parser = MatpowerParser()
    return parser.parse_file(filepath)


# Bus data column indices (MATPOWER format)
BUS_I = 0       # bus number
BUS_TYPE = 1    # bus type (1=PQ, 2=PV, 3=ref, 4=isolated)
PD = 2          # real power demand (MW)
QD = 3          # reactive power demand (MVAr)
GS = 4          # shunt conductance (MW demanded at V = 1.0 p.u.)
BS = 5          # shunt susceptance (MVAr injected at V = 1.0 p.u.)
BUS_AREA = 6    # area number
VM = 7          # voltage magnitude (p.u.)
VA = 8          # voltage angle (degrees)
BASE_KV = 9     # base voltage (kV)
ZONE = 10       # loss zone
VMAX = 11       # maximum voltage magnitude (p.u.)
VMIN = 12       # minimum voltage magnitude (p.u.)

# Generator data column indices
GEN_BUS = 0     # bus number
PG = 1          # real power output (MW)
QG = 2          # reactive power output (MVAr)
QMAX = 3        # maximum reactive power output (MVAr)
QMIN = 4        # minimum reactive power output (MVAr)
VG = 5          # voltage magnitude setpoint (p.u.)
MBASE = 6       # total MVA base of machine
GEN_STATUS = 7  # machine status (1=in-service, 0=out-of-service)
PMAX = 8        # maximum real power output (MW)
PMIN = 9        # minimum real power output (MW)

# Branch data column indices
F_BUS = 0       # from bus number
T_BUS = 1       # to bus number
BR_R = 2        # resistance (p.u.)
BR_X = 3        # reactance (p.u.)
BR_B = 4        # total line charging susceptance (p.u.)
RATE_A = 5      # MVA rating A (long term rating)
RATE_B = 6      # MVA rating B (short term rating)
RATE_C = 7      # MVA rating C (emergency rating)
TAP = 8         # transformer off nominal turns ratio
SHIFT = 9       # transformer phase shift angle (degrees)
BR_STATUS = 10  # initial branch status (1=in-service, 0=out-of-service)
ANGMIN = 11     # minimum angle difference (degrees)
ANGMAX = 12     # maximum angle difference (degrees)
