"""
MATPOWER to SVG Converter

Main module for converting MATPOWER case files to SVG topology diagrams.
Provides command-line interface and configuration options.
"""

import argparse
import json
import os
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
from matpower_parser import <PERSON>power<PERSON><PERSON><PERSON>, MatpowerCase
from layout_algorithms import LayoutConfig
from svg_generator import SVGGenerator, SVGStyle, generate_topology_svg


@dataclass
class ConversionConfig:
    """Complete configuration for MATPOWER to SVG conversion."""
    # Layout settings
    layout_algorithm: str = "hierarchical"  # hierarchical, spring, radial, geographic
    layout_config: LayoutConfig = None
    
    # SVG styling
    svg_style: SVGStyle = None
    
    # Output settings
    output_file: Optional[str] = None
    title: Optional[str] = None
    
    def __post_init__(self):
        if self.layout_config is None:
            self.layout_config = LayoutConfig()
        if self.svg_style is None:
            self.svg_style = SVGStyle()


class MatpowerToSVGConverter:
    """Main converter class for MATPOWER to SVG conversion."""
    
    def __init__(self, config: Optional[ConversionConfig] = None):
        self.config = config or ConversionConfig()
        self.parser = MatpowerParser()
        
    def convert_file(self, input_file: str, output_file: Optional[str] = None) -> str:
        """
        Convert MATPOWER case file to SVG.
        
        Args:
            input_file: Path to MATPOWER .m file
            output_file: Optional output SVG file path
            
        Returns:
            SVG content as string
        """
        # Parse MATPOWER case
        case = self.parser.parse_file(input_file)
        
        # Generate SVG
        svg_content = self._generate_svg(case)
        
        # Save to file if specified
        if output_file or self.config.output_file:
            output_path = output_file or self.config.output_file
            with open(output_path, 'w') as f:
                f.write(svg_content)
            print(f"SVG saved to: {output_path}")
        
        return svg_content
    
    def convert_case(self, case: MatpowerCase, output_file: Optional[str] = None) -> str:
        """
        Convert MATPOWER case object to SVG.
        
        Args:
            case: MATPOWER case data
            output_file: Optional output SVG file path
            
        Returns:
            SVG content as string
        """
        svg_content = self._generate_svg(case)
        
        if output_file:
            with open(output_file, 'w') as f:
                f.write(svg_content)
            print(f"SVG saved to: {output_file}")
        
        return svg_content
    
    def _generate_svg(self, case: MatpowerCase) -> str:
        """Generate SVG from MATPOWER case using current configuration."""
        return generate_topology_svg(
            case=case,
            layout_algorithm=self.config.layout_algorithm,
            style=self.config.svg_style,
            title=self.config.title or f"{case.name} Power System Topology"
        )
    
    def load_config_from_file(self, config_file: str) -> None:
        """Load configuration from JSON file."""
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        
        # Update configuration
        if 'layout_algorithm' in config_data:
            self.config.layout_algorithm = config_data['layout_algorithm']
        
        if 'title' in config_data:
            self.config.title = config_data['title']
        
        if 'output_file' in config_data:
            self.config.output_file = config_data['output_file']
        
        # Update layout config
        if 'layout_config' in config_data:
            layout_data = config_data['layout_config']
            for key, value in layout_data.items():
                if hasattr(self.config.layout_config, key):
                    setattr(self.config.layout_config, key, value)
        
        # Update SVG style
        if 'svg_style' in config_data:
            style_data = config_data['svg_style']
            for key, value in style_data.items():
                if hasattr(self.config.svg_style, key):
                    setattr(self.config.svg_style, key, value)
    
    def save_config_to_file(self, config_file: str) -> None:
        """Save current configuration to JSON file."""
        config_dict = {
            'layout_algorithm': self.config.layout_algorithm,
            'title': self.config.title,
            'output_file': self.config.output_file,
            'layout_config': asdict(self.config.layout_config),
            'svg_style': asdict(self.config.svg_style)
        }
        
        with open(config_file, 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        print(f"Configuration saved to: {config_file}")


def create_sample_config() -> ConversionConfig:
    """Create a sample configuration with custom styling."""
    config = ConversionConfig()
    
    # Custom layout settings
    config.layout_config.width = 1200.0
    config.layout_config.height = 900.0
    config.layout_config.bus_spacing = 50.0
    config.layout_config.level_spacing = 100.0
    
    # Custom SVG styling
    config.svg_style.width = 1200.0
    config.svg_style.height = 900.0
    config.svg_style.bus_radius = 4.0
    config.svg_style.line_width = 1.5
    config.svg_style.label_font_size = 12.0
    
    # Color scheme
    config.svg_style.pq_bus_color = "darkblue"
    config.svg_style.pv_bus_color = "green"
    config.svg_style.ref_bus_color = "red"
    config.svg_style.line_color = "darkgray"
    
    return config


def main():
    """Command-line interface for MATPOWER to SVG conversion."""
    parser = argparse.ArgumentParser(description="Convert MATPOWER case files to SVG topology diagrams")
    
    parser.add_argument("input_file", help="Input MATPOWER case file (.m)")
    parser.add_argument("-o", "--output", help="Output SVG file")
    parser.add_argument("-l", "--layout", choices=["hierarchical", "spring", "radial", "geographic"],
                       default="hierarchical", help="Layout algorithm")
    parser.add_argument("-t", "--title", help="Diagram title")
    parser.add_argument("-c", "--config", help="Configuration file (JSON)")
    parser.add_argument("--save-config", help="Save current configuration to file")
    parser.add_argument("--width", type=float, help="SVG width")
    parser.add_argument("--height", type=float, help="SVG height")
    parser.add_argument("--bus-radius", type=float, help="Bus symbol radius")
    parser.add_argument("--line-width", type=float, help="Transmission line width")
    
    args = parser.parse_args()
    
    # Create converter with default config
    config = ConversionConfig()
    converter = MatpowerToSVGConverter(config)
    
    # Load configuration file if specified
    if args.config:
        converter.load_config_from_file(args.config)
    
    # Apply command-line overrides
    if args.layout:
        converter.config.layout_algorithm = args.layout
    if args.title:
        converter.config.title = args.title
    if args.output:
        converter.config.output_file = args.output
    if args.width:
        converter.config.svg_style.width = args.width
        converter.config.layout_config.width = args.width
    if args.height:
        converter.config.svg_style.height = args.height
        converter.config.layout_config.height = args.height
    if args.bus_radius:
        converter.config.svg_style.bus_radius = args.bus_radius
    if args.line_width:
        converter.config.svg_style.line_width = args.line_width
    
    # Save configuration if requested
    if args.save_config:
        converter.save_config_to_file(args.save_config)
    
    # Convert file
    try:
        svg_content = converter.convert_file(args.input_file)
        
        if not args.output and not converter.config.output_file:
            # Print to stdout if no output file specified
            print(svg_content)
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
