"""
SVG Generation Engine for Power System Topology Visualization

This module provides functionality to generate SVG diagrams from MATPOWER
case data with automatic layout and styling.
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import xml.etree.ElementTree as ET
from xml.dom import minidom
from matpower_parser import MatpowerCase, F_BUS, T_BUS, BUS_I, BUS_TYPE, GEN_BUS
from layout_algorithms import BusPosition, NetworkLayoutEngine, LayoutConfig


@dataclass
class SVGStyle:
    """Configuration for SVG styling."""
    # Canvas
    width: float = 1000.0
    height: float = 800.0
    background_color: str = "white"
    
    # Buses
    bus_radius: float = 3.0
    bus_color: str = "black"
    bus_stroke_width: float = 1.0
    
    # Bus types (different colors/styles)
    pq_bus_color: str = "black"      # Load buses
    pv_bus_color: str = "blue"       # Generator buses  
    ref_bus_color: str = "red"       # Reference bus
    isolated_bus_color: str = "gray" # Isolated buses
    
    # Transmission lines
    line_color: str = "black"
    line_width: float = 1.0
    line_opacity: float = 1.0
    
    # Labels
    label_font_family: str = "sans-serif"
    label_font_size: float = 10.0
    label_color: str = "black"
    
    # Generators
    gen_symbol_size: float = 8.0
    gen_symbol_color: str = "green"
    
    # Title and caption
    title_font_size: float = 16.0
    title_color: str = "darkblue"
    caption_font_size: float = 14.0
    caption_color: str = "green"


class SVGGenerator:
    """Generates SVG diagrams from MATPOWER case data."""
    
    def __init__(self, style: Optional[SVGStyle] = None):
        self.style = style or SVGStyle()
        self.layout_engine = NetworkLayoutEngine()
        
    def generate_svg(self, case: MatpowerCase, 
                    layout_algorithm: str = "hierarchical",
                    title: Optional[str] = None) -> str:
        """
        Generate SVG diagram from MATPOWER case.
        
        Args:
            case: MATPOWER case data
            layout_algorithm: Layout algorithm to use
            title: Optional title for the diagram
            
        Returns:
            SVG content as string
        """
        # Generate layout
        positions = self.layout_engine.generate_layout(case, layout_algorithm)
        
        # Create SVG root element
        svg = ET.Element("svg")
        svg.set("width", str(self.style.width))
        svg.set("height", str(self.style.height))
        svg.set("xmlns", "http://www.w3.org/2000/svg")
        
        # Add styles
        self._add_styles(svg)
        
        # Add title if provided
        if title:
            self._add_title(svg, title)
        
        # Add transmission lines (draw first so they appear behind buses)
        self._add_transmission_lines(svg, case, positions)
        
        # Add buses
        self._add_buses(svg, case, positions)
        
        # Add generators
        self._add_generators(svg, case, positions)
        
        # Add labels
        self._add_bus_labels(svg, case, positions)
        
        # Add caption
        self._add_caption(svg, case)
        
        # Convert to string
        return self._prettify_xml(svg)
    
    def _add_styles(self, svg: ET.Element) -> None:
        """Add CSS styles to SVG."""
        style = ET.SubElement(svg, "style")
        style.text = f"""
    .bus {{
      fill: {self.style.bus_color};
      stroke: {self.style.bus_color};
      stroke-width: {self.style.bus_stroke_width};
    }}
    .pq-bus {{
      fill: {self.style.pq_bus_color};
    }}
    .pv-bus {{
      fill: {self.style.pv_bus_color};
    }}
    .ref-bus {{
      fill: {self.style.ref_bus_color};
    }}
    .isolated-bus {{
      fill: {self.style.isolated_bus_color};
    }}
    .line {{
      stroke: {self.style.line_color};
      stroke-width: {self.style.line_width};
      fill: none;
      opacity: {self.style.line_opacity};
    }}
    .label {{
      font-family: {self.style.label_font_family};
      font-size: {self.style.label_font_size}px;
      fill: {self.style.label_color};
      text-anchor: start;
    }}
    .generator {{
      fill: {self.style.gen_symbol_color};
      stroke: {self.style.gen_symbol_color};
      stroke-width: 1;
    }}
    .title {{
      font-family: {self.style.label_font_family};
      font-size: {self.style.title_font_size}px;
      font-weight: bold;
      fill: {self.style.title_color};
      text-anchor: middle;
    }}
    .caption {{
      font-family: {self.style.label_font_family};
      font-size: {self.style.caption_font_size}px;
      font-weight: bold;
      fill: {self.style.caption_color};
    }}
        """
    
    def _add_title(self, svg: ET.Element, title: str) -> None:
        """Add title to SVG."""
        title_elem = ET.SubElement(svg, "text")
        title_elem.set("x", str(self.style.width / 2))
        title_elem.set("y", "30")
        title_elem.set("class", "title")
        title_elem.text = title
    
    def _add_transmission_lines(self, svg: ET.Element, case: MatpowerCase, 
                               positions: Dict[int, BusPosition]) -> None:
        """Add transmission lines to SVG."""
        if len(case.branch.shape) <= 1:
            return
            
        for branch_data in case.branch:
            from_bus = int(branch_data[F_BUS])
            to_bus = int(branch_data[T_BUS])
            
            if from_bus in positions and to_bus in positions:
                from_pos = positions[from_bus]
                to_pos = positions[to_bus]
                
                line = ET.SubElement(svg, "line")
                line.set("x1", str(from_pos.x))
                line.set("y1", str(from_pos.y))
                line.set("x2", str(to_pos.x))
                line.set("y2", str(to_pos.y))
                line.set("class", "line")
    
    def _add_buses(self, svg: ET.Element, case: MatpowerCase, 
                   positions: Dict[int, BusPosition]) -> None:
        """Add bus symbols to SVG."""
        for bus_id, pos in positions.items():
            circle = ET.SubElement(svg, "circle")
            circle.set("cx", str(pos.x))
            circle.set("cy", str(pos.y))
            circle.set("r", str(self.style.bus_radius))
            
            # Set class based on bus type
            if pos.bus_type == 1:
                circle.set("class", "bus pq-bus")
            elif pos.bus_type == 2:
                circle.set("class", "bus pv-bus")
            elif pos.bus_type == 3:
                circle.set("class", "bus ref-bus")
            else:
                circle.set("class", "bus isolated-bus")
    
    def _add_generators(self, svg: ET.Element, case: MatpowerCase, 
                       positions: Dict[int, BusPosition]) -> None:
        """Add generator symbols to SVG."""
        if len(case.gen.shape) <= 1:
            return
            
        for gen_data in case.gen:
            gen_bus = int(gen_data[GEN_BUS])
            
            if gen_bus in positions:
                pos = positions[gen_bus]
                
                # Draw generator symbol (circle with G)
                gen_circle = ET.SubElement(svg, "circle")
                gen_circle.set("cx", str(pos.x + self.style.gen_symbol_size))
                gen_circle.set("cy", str(pos.y - self.style.gen_symbol_size))
                gen_circle.set("r", str(self.style.gen_symbol_size / 2))
                gen_circle.set("class", "generator")
                
                # Add "G" text
                gen_text = ET.SubElement(svg, "text")
                gen_text.set("x", str(pos.x + self.style.gen_symbol_size))
                gen_text.set("y", str(pos.y - self.style.gen_symbol_size + 3))
                gen_text.set("class", "label")
                gen_text.set("text-anchor", "middle")
                gen_text.set("font-size", str(self.style.gen_symbol_size))
                gen_text.text = "G"
    
    def _add_bus_labels(self, svg: ET.Element, case: MatpowerCase, 
                       positions: Dict[int, BusPosition]) -> None:
        """Add bus number labels to SVG."""
        for bus_id, pos in positions.items():
            label = ET.SubElement(svg, "text")
            label.set("x", str(pos.x + self.style.bus_radius + 2))
            label.set("y", str(pos.y + self.style.label_font_size / 3))
            label.set("class", "label")
            label.text = str(bus_id)
    
    def _add_caption(self, svg: ET.Element, case: MatpowerCase) -> None:
        """Add figure caption to SVG."""
        bus_count = case.get_bus_count()
        caption_text = f"FIGURE: {case.name} - {bus_count}-bus Power System"
        
        caption = ET.SubElement(svg, "text")
        caption.set("x", "50")
        caption.set("y", str(self.style.height - 30))
        caption.set("class", "caption")
        caption.text = caption_text
    
    def _prettify_xml(self, elem: ET.Element) -> str:
        """Return a pretty-printed XML string."""
        rough_string = ET.tostring(elem, 'unicode')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ")


def generate_topology_svg(case: MatpowerCase, 
                         layout_algorithm: str = "hierarchical",
                         style: Optional[SVGStyle] = None,
                         title: Optional[str] = None) -> str:
    """
    Convenience function to generate SVG from MATPOWER case.
    
    Args:
        case: MATPOWER case data
        layout_algorithm: Layout algorithm ('hierarchical', 'spring', 'radial')
        style: SVG styling configuration
        title: Optional title for the diagram
        
    Returns:
        SVG content as string
    """
    generator = SVGGenerator(style)
    return generator.generate_svg(case, layout_algorithm, title)
